'use client'

import { Sidebar, Navbar } from '@/components'

export function DashboardLayout({ children }: { children?: React.ReactNode }) {
  return (
    <div className="flex h-screen">
      <div className="drawer lg:drawer-open gap-4">
        <Sidebar />
        <div className="drawer-content flex flex-col transition-all duration-300 ">
          <div className="flex flex-col h-full my-4 mr-4">
            <Navbar />
            <main className="flex-1 overflow-auto" role="main" aria-label="Main content">
              {children}
            </main>
          </div>
        </div>
      </div>
    </div>
  )
}
